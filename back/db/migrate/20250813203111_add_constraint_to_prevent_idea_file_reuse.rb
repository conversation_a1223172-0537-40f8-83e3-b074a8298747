class AddConstraintToPreventIdeaFileReuse < ActiveRecord::Migration[7.1]
  def change
    # Modify file_attachments to add an exclusion constraint that prevents creating multiple
    # file_attachments for the same file if it is already attached to an idea. In other words,
    # block any new file_attachment with the same file_id and attachable_type = 'Idea'.

    execute <<~SQL
      ALTER TABLE file_attachments
        ADD CONSTRAINT prevent_idea_file_reuse
        EXCLUDE USING gist (
          file_id WITH =,
          attachable_type WITH =
        ) WHERE (attachable_type = 'Idea');
    SQL
  end
end
