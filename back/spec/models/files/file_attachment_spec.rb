# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Files::FileAttachment do
  subject(:attachment) { build(:file_attachment) }

  it { is_expected.to be_valid }

  describe 'associations' do
    it { is_expected.to belong_to(:file).class_name('Files::File').inverse_of(:attachments) }
    it { is_expected.to belong_to(:attachable) }
  end

  describe 'validations' do
    it 'validates uniqueness of file_id scoped to attachable_type and attachable_id' do
      attachment = create(:file_attachment)
      new_attachment = build(:file_attachment, file: attachment.file, attachable: attachment.attachable)
      expect(new_attachment).not_to be_valid
    end

    it { is_expected.to validate_inclusion_of(:attachable_type).in_array(described_class::ATTACHABLE_TYPES) }

    describe '#validate_file_belongs_to_project' do
      it 'is invalid when the file and resource belong to different projects' do
        project = create(:project)
        file = create(:file)

        attachables = [
          project,
          create(:event, project: project),
          create(:idea, project: project),
          create(:phase, project: project)
        ]

        attachables.each do |attachable|
          attachment = build(:file_attachment, attachable: attachable, file: file)
          expect(attachment).not_to be_valid
        end
      end

      it 'is valid when the file and resource belong to the same project' do
        project = create(:project)
        file = create(:file, projects: [project])

        attachables = [
          project,
          create(:event, project: project),
          create(:idea, project: project),
          create(:phase, project: project)
        ]

        attachables.each do |attachable|
          attachment = build(:file_attachment, attachable: attachable, file: file)
          expect(attachment).to be_valid
        end
      end
    end

    describe '#validate_file_not_attached_to_idea_when_attaching_to_other_type' do
      let(:project) { create(:project) }
      let(:file) { create(:file, projects: [project]) }

      it 'prevents attaching a file to other resources when already attached to an idea' do
        idea = create(:idea, project: project)
        create(:file_attachment, file: file, attachable: idea)

        # Try to attach the same file to different resource types
        attachables = [
          project,
          create(:event, project: project),
          create(:phase, project: project),
          create(:static_page)
        ]

        attachables.each do |attachable|
          attachment = build(:file_attachment, file: file, attachable: attachable)
          expect(attachment).not_to be_valid
          expect(attachment.errors[:file]).to include('cannot be attached to other resources because it is already attached to an idea')
        end
      end

      it 'allows attaching a file to other resources when not attached to any idea' do
        # Try to attach file to different resource types when no idea attachment exists
        attachables = [
          project,
          create(:event, project: project),
          create(:phase, project: project)
        ]

        attachables.each do |attachable|
          attachment = build(:file_attachment, file: file, attachable: attachable)
          expect(attachment).to be_valid
        end
      end

      it 'allows attaching a file to multiple ideas' do
        idea1 = create(:idea, project: project)
        idea2 = create(:idea, project: project)

        attachment1 = create(:file_attachment, file: file, attachable: idea1)
        expect(attachment1).to be_valid

        # This should fail due to the existing uniqueness constraint for ideas
        attachment2 = build(:file_attachment, file: file, attachable: idea2)
        expect(attachment2).not_to be_valid
      end
    end

    describe '#validate_file_not_already_attached_when_attaching_to_idea' do
      let(:project) { create(:project) }
      let(:file) { create(:file, projects: [project]) }

      it 'prevents attaching a file to an idea when already attached to other resources' do
        # First attach file to various other resource types
        attachables = [
          project,
          create(:event, project: project),
          create(:phase, project: project)
        ]

        attachables.each do |attachable|
          create(:file_attachment, file: file, attachable: attachable)

          # Now try to attach the same file to an idea
          idea = create(:idea, project: project)
          attachment = build(:file_attachment, file: file, attachable: idea)
          expect(attachment).not_to be_valid
          expect(attachment.errors[:file]).to include('cannot be attached to an idea because it is already attached to another resource')
        end
      end

      it 'allows attaching a file to an idea when not attached to any other resources' do
        idea = create(:idea, project: project)
        attachment = build(:file_attachment, file: file, attachable: idea)
        expect(attachment).to be_valid
      end

      it 'allows attaching different files to the same idea' do
        idea = create(:idea, project: project)
        file1 = create(:file, projects: [project])
        file2 = create(:file, projects: [project])

        attachment1 = create(:file_attachment, file: file1, attachable: idea)
        expect(attachment1).to be_valid

        # This should fail due to the existing uniqueness constraint for ideas (one file per idea)
        attachment2 = build(:file_attachment, file: file2, attachable: idea)
        expect(attachment2).not_to be_valid
      end
    end

    describe 'comprehensive validation scenarios' do
      let(:project) { create(:project) }
      let(:file) { create(:file, projects: [project]) }

      it 'handles the complete workflow: attach to idea first, then try other attachments' do
        idea = create(:idea, project: project)

        # Step 1: Attach file to idea - should succeed
        idea_attachment = create(:file_attachment, file: file, attachable: idea)
        expect(idea_attachment).to be_valid

        # Step 2: Try to attach same file to other resources - should fail
        other_attachables = [
          project,
          create(:event, project: project),
          create(:phase, project: project),
          create(:static_page)
        ]

        other_attachables.each do |attachable|
          attachment = build(:file_attachment, file: file, attachable: attachable)
          expect(attachment).not_to be_valid
          expect(attachment.errors[:file]).to include('cannot be attached to other resources because it is already attached to an idea')
        end
      end

      it 'handles the reverse workflow: attach to other resources first, then try idea attachment' do
        event = create(:event, project: project)

        # Step 1: Attach file to event - should succeed
        event_attachment = create(:file_attachment, file: file, attachable: event)
        expect(event_attachment).to be_valid

        # Step 2: Try to attach same file to idea - should fail
        idea = create(:idea, project: project)
        idea_attachment = build(:file_attachment, file: file, attachable: idea)
        expect(idea_attachment).not_to be_valid
        expect(idea_attachment.errors[:file]).to include('cannot be attached to an idea because it is already attached to another resource')
      end

      it 'allows multiple attachments to different non-idea resources for the same file' do
        # This tests that the validation only restricts idea-related scenarios
        attachables = [
          project,
          create(:event, project: project),
          create(:phase, project: project)
        ]

        attachables.each do |attachable|
          attachment = create(:file_attachment, file: file, attachable: attachable)
          expect(attachment).to be_valid
        end

        # Verify all attachments exist
        expect(file.attachments.count).to eq(3)
        expect(file.attachments.where(attachable_type: 'Idea')).to be_empty
      end

      it 'validates correctly when file is nil' do
        # Test edge case where file is not set
        attachment = build(:file_attachment, file: nil, attachable: create(:idea, project: project))
        # Should be invalid due to belongs_to validation, not our custom validations
        expect(attachment).not_to be_valid
        expect(attachment.errors[:file]).not_to include('cannot be attached to other resources because it is already attached to an idea')
        expect(attachment.errors[:file]).not_to include('cannot be attached to an idea because it is already attached to another resource')
      end
    end
  end

  describe 'ATTACHABLE_TYPES' do
    it 'ensures all types include Files::FileAttachable concern' do
      described_class::ATTACHABLE_TYPES.each do |type|
        model_class = type.constantize

        expect(model_class.included_modules).to include(Files::FileAttachable),
          "#{type} should include Files::FileAttachable concern"
      end
    end
  end
end
